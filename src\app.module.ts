import { Module } from '@nestjs/common';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './modules/user/user.module';
import { StoreModule } from './modules/store/store.module';
import { UserBranchAssignmentModule } from './modules/user-branch-assignment/user-branch-assignment.module';
import { BranchModule } from './modules/branch/branch.module';
import { KioskModule } from './modules/kiosk/kiosk.module';
import { PosModule } from './modules/pos/pos.module';
import { LanguageModule } from './modules/language/language.module';
import { TranslationModule } from './modules/translation/translation.module';
import { OptionModule } from './modules/option/option.module';
import { VariationModule } from './modules/variation/variation.module';
import { CategoryModule } from './modules/category/category.module';
import { ProductModule } from './modules/product/product.module';
import { ProductVariationsModule } from './modules/product-variations/product-variations.module';
import { ProductOptionsModule } from './modules/product-options/product-options.module';
import { PosSessionModule } from './modules/pos-session/pos-session.module';
import { TaxModule } from './modules/tax/tax.module';
import { DiscountModule } from './modules/discount/discount.module';
import { ProductDiscountsModule } from './modules/product-discounts/product-discounts.module';
import { ProductTaxesModule } from './modules/product-taxes/product-taxes.module';
import { AddonsModule } from './modules/addons/addons.module';
import { OrderModule } from './modules/order/order.module';
import { OrderDetailsModule } from './modules/order-details/order-details.module';
import { PaymentMethodModule } from './modules/payment-method/payment-method.module';
import { PaymentModule } from './modules/payment/payment.module';
import { CouponModule } from './modules/coupon/coupon.module';
import { CustomerModule } from './modules/customer/customer.module';
import { LoyaltyModule } from './modules/loyalty/loyalty.module';
import { NotificationModule } from './modules/notification/notification.module';
import { SupplierModule } from './modules/supplier/supplier.module';
import { PurchaseModule } from './modules/purchase/purchase.module';
import { PurchaseDetailsModule } from './modules/purchase-details/purchase-details.module';
import { InventoryModule } from './modules/inventory/inventory.module';
import { OrderSummaryModule } from './modules/order-summary/order-summary.module';
import { PurchaseSummaryModule } from './modules/purchase-summary/purchase-summary.module';
import { OrderCategorySummaryModule } from './modules/order-category-summary/order-category-summary.module';
import { AuditLogModule } from './modules/audit-log/audit-log.module';
import { AuthModule } from './modules/auth/auth.module';
import { UserAuthorityModule } from './modules/user-authority/user-authority.module';
import { AuthorityModule } from './modules/authority/authority.module';
import { TaxStoreModule } from './modules/tax-store/tax-store.module';
import { TaxCategoryModule } from './modules/tax-category/tax-category.module';
import { TaxProductModule } from './modules/tax-product/tax-product.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { SharedModule } from './modules/shared/shared.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { UnitModule } from './modules/unit/unit.module';
import { JwtModule } from '@nestjs/jwt';
import { RolesGuard } from './modules/auth/guards/roles.guard';
import { TableModule } from './modules/tables/tables.module';
import { ContactModule } from './modules/contact/contact.module';
import { CommentModule } from './modules/comment/comment.module';
import { ServiceModule } from './modules/service/service.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get<string>('DB_HOST'),
        port: configService.get<number>('DATABASE_PORT'),
        username: configService.get<string>('DATABASE_USER'),
        password: configService.get<string>('DATABASE_PASSWORD'),
        database: configService.get<string>('DATABASE_NAME'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get<boolean>('DB_SYNCHRONIZE') || true,
        namingStrategy: new SnakeNamingStrategy(),
      }),
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
      serveRoot: '/uploads/',
    }),
    UserModule,
    StoreModule,
    UserBranchAssignmentModule,
    BranchModule,
    KioskModule,
    PosModule,
    LanguageModule,
    TranslationModule,
    OptionModule,
    VariationModule,
    CategoryModule,
    ProductModule,
    ProductVariationsModule,
    ProductOptionsModule,
    PosSessionModule,
    TaxModule,
    DiscountModule,
    ProductDiscountsModule,
    ProductTaxesModule,
    AddonsModule,
    OrderModule,
    OrderDetailsModule,
    PaymentMethodModule,
    PaymentModule,
    CouponModule,
    CustomerModule,
    LoyaltyModule,
    NotificationModule,
    SupplierModule,
    PurchaseModule,
    PurchaseDetailsModule,
    InventoryModule,
    OrderSummaryModule,
    PurchaseSummaryModule,
    OrderCategorySummaryModule,
    AuditLogModule,
    AuthModule,
    UserAuthorityModule,
    AuthorityModule,
    TaxStoreModule,
    TaxCategoryModule,
    TaxProductModule,
    SharedModule,
    UnitModule,
    JwtModule,
    TableModule,
    ContactModule,
    CommentModule,
    ServiceModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    RolesGuard,
  ],
})
export class AppModule {}
