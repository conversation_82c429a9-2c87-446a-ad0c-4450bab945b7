import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { VersioningType } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Set global API prefix
  app.setGlobalPrefix('api');

  // Enable global validation pipes
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Enable versioning
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // cors
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,POST,PUT,DELETE,PATCH',
    allowedHeaders: 'Content-Type,Authorization',
    credentials: true,
  });

  // Swagger Configuration
  const config = new DocumentBuilder()
    .setTitle('API Documentation')
    .setDescription('API documentation for the application')
    .setVersion('1.0')
    .addTag('API')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Set Swagger documentation route to '/api/docs'
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(6061);
}
bootstrap();

/**
 
Options : DONE,
Category : DONE,
variations : Add relation with branch done.
products : add realtions with branch done.
Orders ; doene,
order_category_summary : todo,
order_summary : todo,
POS : todo,
audit_log : TODO,
coupon : TODO,
discount : TODO,
Inventory : TODO,
kiosk: TODO,
loyalty: TODO,
purchase_summary : todo,

*/
