import {
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { AuthorityService } from '../authority/authority.service';
import { User } from '../user/entities/user.entity';
import { UserService } from '../user/user.service';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly authorityService: AuthorityService,
  ) {}

  async getMe(userId: string) {
    return this.userService.getUserWithRelations(userId);
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.userService.findByEmail(email);

    if (user && (await bcrypt.compare(password, user.password))) {
      const { password, ...result } = user;
      return result;
    }
    throw new UnauthorizedException('Invalid credentials');
  }

  async login(user: User) {
    const payload = {
      username: user.email,
      sub: user.id,
      roles: user.authorities.map(
        (userAuthority) => userAuthority.authority.name,
      ),
    };

    return {
      access_token: this.jwtService.sign(payload),
    };
  }

  async register(registerDto: RegisterDto) {
    const existingUser = await this.userService.findByEmail(registerDto.email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    const newUser = await this.userService.create({
      ...registerDto,
      password: hashedPassword,
      login: registerDto.email,
    });

    const userRole = await this.authorityService.findOne('ROLE_USER');
    await this.userService.assignRole(newUser.id, userRole);

    const { password, ...result } = newUser;
    return result;
  }
}
