import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';

@ApiTags('Branches')
@Controller('branches')
export class BranchController {
  constructor(private readonly branchService: BranchService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new branch' })
  @ApiBody({ type: CreateBranchDto })
  async create(@Body() createBranchDto: CreateBranchDto) {
    return await this.branchService.create(createBranchDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all branches' })
  async findAll() {
    return await this.branchService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a branch by id' })
  async findOne(@Param('id') id: string) {
    return await this.branchService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a branch by id' })
  @ApiBody({ type: UpdateBranchDto })
  async update(
    @Param('id') id: string,
    @Body() updateBranchDto: UpdateBranchDto,
  ) {
    return await this.branchService.update(id, updateBranchDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a branch by id' })
  async remove(@Param('id') id: string) {
    return await this.branchService.remove(id);
  }

  @Get(':id/kiosks')
  @ApiOperation({ summary: 'Get all kiosks by branch id' })
  async getBranchKiosks(@Param('id') id: string) {
    return await this.branchService.getBranchKiosks(id);
  }

  @Get(':id/pos')
  @ApiOperation({ summary: 'Get all pos by branch id' })
  async getBranchPos(@Param('id') id: string) {
    return await this.branchService.getBranchPos(id);
  }
}
