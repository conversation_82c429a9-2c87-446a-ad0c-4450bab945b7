import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Option } from 'src/modules/option/entities/option.entity';
import { UploadService } from '../shared/upload.service';
import { Store } from '../store/entities/store.entity';
import { StoreService } from '../store/store.service';
import { Variation } from '../variation/entities/variation.entity';
import { BranchController } from './branch.controller';
import { BranchService } from './branch.service';
import { Branch } from './entities/branch.entity';
@Module({
  controllers: [BranchController],
  providers: [BranchService, UploadService, StoreService],
  imports: [TypeOrmModule.forFeature([Branch, Option, Variation, Store])],
  exports: [BranchService],
})
export class BranchModule {}
