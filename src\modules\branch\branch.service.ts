import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Branch } from './entities/branch.entity';
import { Kiosk } from '../kiosk/entities/kiosk.entity';
import { Pos } from '../pos/entities/pos.entity';
import { UploadService } from '../shared/upload.service';
import { StoreService } from '../store/store.service';

@Injectable()
export class BranchService {
  constructor(
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
    private readonly uploadService: UploadService,
    private readonly storeService: StoreService,
  ) {}

  async create(createBranchDto: CreateBranchDto): Promise<Branch> {
    const { name, storeId } = createBranchDto;
    const store = await this.storeService.findOne(storeId);
    await this.findBranchByNameAndStoreId(name, store.id);
    const branch = this.branchRepository.create(createBranchDto);
    branch.store = store;
    return await this.branchRepository.save(branch);
  }

  async findAll(): Promise<Branch[]> {
    return await this.branchRepository.find({ relations: ['store'] });
  }

  async findOne(id: string): Promise<Branch> {
    const branch = await this.branchRepository.findOne({
      where: { id },
      relations: ['store'],
    });
    if (!branch) {
      throw new NotFoundException(`Branch with id ${id} not found`);
    }
    return branch;
  }

  async update(id: string, updateBranchDto: UpdateBranchDto): Promise<Branch> {
    const { name, address, phone, email, logoUrl } = updateBranchDto;
    const branch = await this.findOne(id);
    if (name && name !== branch.name) {
      await this.findBranchByNameAndStoreId(name, branch.store.id);
      branch.name = name;
    }
    if (address && address !== branch?.address) {
      branch.address = address;
    }
    if (phone && phone !== branch?.phone) {
      branch.phone = phone;
    }
    if (email && email !== branch?.email) {
      branch.email = email;
    }
    if (logoUrl && logoUrl !== branch?.logoUrl) {
      if (branch.logoUrl) {
        await this.uploadService.deleteFile(branch.logoUrl);
      }
      branch.logoUrl = logoUrl;
    }
    return await this.branchRepository.save(branch);
  }

  async remove(id: string): Promise<void> {
    await this.findOne(id);
    await this.branchRepository.delete(id);
  }

  async getBranchKiosks(id: string): Promise<Kiosk[]> {
    const branch = await this.branchRepository.findOne({
      where: { id },
      relations: ['kiosk'],
    });

    if (!branch) {
      throw new NotFoundException(`Branch with id ${id} not found`);
    }

    return branch.kiosk;
  }

  async getBranchPos(id: string): Promise<Pos[]> {
    const branch = await this.branchRepository.findOne({
      where: { id },
      relations: ['pos'],
    });

    if (!branch) {
      throw new NotFoundException(`Branch with id ${id} not found`);
    }

    return branch.pos;
  }

  async findBranchByNameAndStoreId(
    name: string,
    storeId: string,
  ): Promise<void> {
    const branch = await this.branchRepository.findOne({
      where: { name, store: { id: storeId } },
    });

    if (branch) {
      throw new ConflictException(
        `Branch with name ${name} already exists in store ${branch.store.name}`,
      );
    }
  }
}
