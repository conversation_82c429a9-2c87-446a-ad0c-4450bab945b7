import { IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBranchDto {
  @ApiProperty({
    example: 'Main Branch',
    description: 'The name of the branch',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: '123 Main St',
    description: 'The address of the branch',
  })
  @IsString()
  address: string;

  @ApiProperty({
    example: '1234567890',
    description: 'The phone number of the branch',
  })
  @IsString()
  phone: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the branch',
  })
  @IsString()
  email: string;

  @ApiProperty({
    example: 'https://mainbranch.com/logo.png',
    description: 'The logo URL of the branch',
  })
  @IsString()
  @IsOptional()
  logoUrl: string;

  @ApiProperty({
    example: '1',
    description: 'The store ID of the branch',
  })
  @IsString()
  storeId: string;
}
