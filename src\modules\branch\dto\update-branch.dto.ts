import { IsString } from 'class-validator';
import { IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateBranchDto {
  @ApiProperty({
    example: 'Main Branch',
    description: 'The name of the branch',
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    example: '123 Main St, Anytown, USA',
    description: 'The address of the branch',
  })
  @IsString()
  @IsOptional()
  address: string;

  @ApiProperty({
    example: '+1234567890',
    description: 'The phone number of the branch',
  })
  @IsString()
  @IsOptional()
  phone: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the branch',
  })
  @IsString()
  @IsOptional()
  email: string;

  @ApiProperty({
    example: 'https://mainbranch.com/logo.png',
    description: 'The logo URL of the branch',
  })
  @IsString()
  @IsOptional()
  logoUrl: string;
}
