import {
  En<PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Store } from '../../store/entities/store.entity';
import { UserBranchAssignment } from '../../user-branch-assignment/entities/user-branch-assignment.entity';
import { Order } from 'src/modules/order/entities/order.entity';
import { Coupon } from 'src/modules/coupon/entities/coupon.entity';
import { Pos } from 'src/modules/pos/entities/pos.entity';
import { Kiosk } from 'src/modules/kiosk/entities/kiosk.entity';
import { Category } from 'src/modules/category/entities/category.entity';
import { Option } from 'src/modules/option/entities/option.entity';
import { Variation } from 'src/modules/variation/entities/variation.entity';
import { Product } from 'src/modules/product/entities/product.entity';
import { Table } from 'src/modules/tables/entities/table.entity';

@Entity('branch')
export class Branch {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column()
  address: string;

  @Column()
  phone: string;

  @Column()
  email: string;

  @Column({ nullable: true, default: 'branch.png' })
  logoUrl: string;

  @ManyToOne(() => Store, (store) => store.branches, {
    onDelete: 'CASCADE',
  })
  store: Store;

  @OneToMany(() => Order, (order) => order.branch)
  orders: Order[];

  @OneToMany(() => UserBranchAssignment, (assignment) => assignment.branch)
  userAssignments: UserBranchAssignment[];

  @OneToMany(() => Coupon, (coupon) => coupon.branch)
  coupons: Coupon[];

  @OneToMany(() => Pos, (pos) => pos.branch)
  pos: Pos[];

  @OneToMany(() => Kiosk, (kiosk) => kiosk.branch)
  kiosk: Kiosk[];

  @OneToMany(() => Category, (category) => category.branch)
  categories: Category[];

  @OneToMany(() => Option, (option) => option.branch)
  options: Option[];

  @OneToMany(() => Variation, (variation) => variation.branch)
  variations: Variation[];

  @OneToMany(() => Product, (product) => product.branch)
  products: Product[];

  @OneToMany(() => Table, (table) => table.branch)
  tables: Table[];
}
