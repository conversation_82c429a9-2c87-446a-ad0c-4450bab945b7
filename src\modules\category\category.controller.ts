import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  SetMetadata,
  Req,
  NotFoundException,
  Query,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Authority } from 'src/common/constants/authority.constants';

@ApiTags('categories')
@Controller('categories/branch/:branchId')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @ApiOperation({ summary: 'Create a new category in branch' })
  @Post()
  @UseGuards(RolesGuard)
  @SetMetadata('roles', [Authority.ADMIN, Authority.OWNER, Authority.MANAGER])
  async create(
    @Param('branchId') branchId: string,
    @Body() createCategoryDto: CreateCategoryDto,
    @Req() req,
  ) {
    return await this.categoryService.createInBranch(
      branchId,
      createCategoryDto,
      req.user.userId,
    );
  }

  @ApiOperation({ summary: 'Get all categories in branch' })
  @Get()
  async findAll(
    @Param('branchId') branchId: string,
    @Req() req,
    @Query('availability') availability?: boolean,
  ) {
    return await this.categoryService.findAllInBranch(
      branchId,
      req.user.userId,
      availability,
    );
  }

  @ApiOperation({ summary: 'Get a category by id in branch' })
  @Get(':id')
  async findOne(
    @Param('branchId') branchId: string,
    @Param('id') id: string,
    @Req() req,
  ) {
    return await this.categoryService.findOneInBranch(
      id,
      branchId,
      req.user.userId,
    );
  }

  @ApiOperation({ summary: 'Update a category by id in branch' })
  @Patch(':id')
  @UseGuards(RolesGuard)
  @SetMetadata('roles', [Authority.ADMIN, Authority.OWNER, Authority.MANAGER])
  async update(
    @Param('branchId') branchId: string,
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
    @Req() req,
  ) {
    const category = await this.categoryService.updateInBranch(
      id,
      branchId,
      updateCategoryDto,
      req.user.userId,
    );
    if (!category) {
      throw new NotFoundException('Category not found in this branch');
    }
    return category;
  }

  @ApiOperation({ summary: 'Delete a category by id in branch' })
  @Delete(':id')
  @UseGuards(RolesGuard)
  @SetMetadata('roles', [Authority.ADMIN, Authority.OWNER, Authority.MANAGER])
  async remove(
    @Param('branchId') branchId: string,
    @Param('id') id: string,
    @Req() req,
  ) {
    const category = await this.categoryService.findOneInBranch(
      id,
      branchId,
      req.user.userId,
    );
    if (!category) {
      throw new NotFoundException('Category not found in this branch');
    }
    return this.categoryService.removeFromBranch(id, branchId, req.user.userId);
  }
}
