import { Module } from '@nestjs/common';
import { CategoryService } from './category.service';
import { CategoryController } from './category.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Category } from './entities/category.entity';
import { BranchService } from '../branch/branch.service';
import { UploadService } from '../shared/upload.service';
import { Branch } from '../branch/entities/branch.entity';
import { Store } from '../store/entities/store.entity';
import { StoreService } from '../store/store.service';
import { Product } from '../product/entities/product.entity';
import { BranchModule } from '../branch/branch.module';
import { UserBranchAssignmentModule } from '../user-branch-assignment/user-branch-assignment.module';
import { UserModule } from '../user/user.module';

@Module({
  controllers: [CategoryController],
  providers: [CategoryService, BranchService, UploadService, StoreService],
  imports: [
    TypeOrmModule.forFeature([Category, Branch, Store, Product]),
    BranchModule,
    UserBranchAssignmentModule,
    UserModule,
  ],
  exports: [CategoryService],
})
export class CategoryModule {}
