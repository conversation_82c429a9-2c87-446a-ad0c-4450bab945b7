import {
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindManyOptions,
  LessThanOrEqual,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import { Category } from './entities/category.entity';
import { BranchService } from '../branch/branch.service';
import { Authority } from 'src/common/constants/authority.constants';
import { UserService } from '../user/user.service';
import { UserBranchAssignmentService } from '../user-branch-assignment/user-branch-assignment.service';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    private readonly userBranchAssignmentService: UserBranchAssignmentService,
    private readonly userService: UserService,
    private readonly branchService: BranchService,
  ) {}

  async findOne(id: string) {
    const category = await this.categoryRepository.findOne({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    return category;
  }

  async canManageBranch(userId: string, branchId: string): Promise<boolean> {
    const user = await this.userService.findOne(userId);
    if (!user) return false;

    // Admin can manage all branches
    if (
      user.authorities.some((auth) => auth.authority.name === Authority.ADMIN)
    ) {
      return true;
    }

    // Check if user is assigned to branch and has required role
    const assignment =
      await this.userBranchAssignmentService.findByUserAndBranch(
        userId,
        branchId,
      );
    if (!assignment) return false;

    const allowedRoles = [Authority.OWNER, Authority.MANAGER];
    return user.authorities.some((auth) =>
      allowedRoles.includes(auth.authority.name as any),
    );
  }

  async createInBranch(
    branchId: string,
    createCategoryDto: CreateCategoryDto,
    userId: string,
  ) {
    if (!(await this.canManageBranch(userId, branchId))) {
      throw new ForbiddenException(
        'You do not have permission to create categories in this branch',
      );
    }

    // Verify branch exists
    await this.branchService.findOne(branchId);

    // Check for existing category with same name in branch
    const existingCategory = await this.categoryRepository.findOne({
      where: {
        name: createCategoryDto.name,
        branch: { id: branchId },
      },
    });
    if (existingCategory) {
      throw new ConflictException(
        `Category ${createCategoryDto.name} already exists in this branch`,
      );
    }

    const category = this.categoryRepository.create({
      ...createCategoryDto,
      branch: { id: branchId },
    });
    return this.categoryRepository.save(category);
  }

  async findAllInBranch(
    branchId: string,
    userId: string,
    availability?: boolean,
  ) {
    // Check if user has access to branch (even read access)
    const hasAccess = await this.userBranchAssignmentService.hasBranchAccess(
      userId,
      branchId,
    );

    if (!hasAccess) {
      throw new ForbiddenException(
        'You do not have access to categories in this branch',
      );
    }

    const now = new Date();
    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    const query: FindManyOptions<Category> = {
      where: { branch: { id: branchId } },
    };

    if (availability) {
      query.where = {
        ...query.where,
        availabilityStatus: true,
        availableFrom: LessThanOrEqual(currentTime),
        availableTo: MoreThanOrEqual(currentTime),
      };
    }

    return this.categoryRepository.find(query);
  }

  async findOneInBranch(id: string, branchId: string, userId: string) {
    const hasAccess = await this.userBranchAssignmentService.hasBranchAccess(
      userId,
      branchId,
    );
    if (!hasAccess) {
      throw new ForbiddenException(
        'You do not have access to categories in this branch',
      );
    }

    const category = await this.categoryRepository.findOne({
      where: { id, branch: { id: branchId } },
    });

    if (!category) {
      throw new NotFoundException(
        `Category with ID ${id} not found in branch ${branchId}`,
      );
    }
    return category;
  }

  async updateInBranch(
    id: string,
    branchId: string,
    updateCategoryDto: UpdateCategoryDto,
    userId: string,
  ) {
    if (!(await this.canManageBranch(userId, branchId))) {
      throw new ForbiddenException(
        'You do not have permission to update categories in this branch',
      );
    }

    const category = await this.categoryRepository.findOne({
      where: { id, branch: { id: branchId } },
    });

    if (!category) {
      throw new NotFoundException(
        `Category with ID ${id} not found in branch ${branchId}`,
      );
    }

    // Check if new name conflicts with existing categories
    if (updateCategoryDto.name && updateCategoryDto.name !== category.name) {
      const existingCategory = await this.categoryRepository.findOne({
        where: {
          name: updateCategoryDto.name,
          branch: { id: branchId },
        },
      });
      if (existingCategory && existingCategory.id !== id) {
        throw new ConflictException(
          `Category ${updateCategoryDto.name} already exists in this branch`,
        );
      }
    }

    Object.assign(category, updateCategoryDto);
    return await this.categoryRepository.update(id, category);
  }

  async removeFromBranch(id: string, branchId: string, userId: string) {
    if (!(await this.canManageBranch(userId, branchId))) {
      throw new ForbiddenException(
        'You do not have permission to delete categories in this branch',
      );
    }

    const category = await this.categoryRepository.findOne({
      where: { id, branch: { id: branchId } },
    });

    if (!category) {
      throw new NotFoundException(
        `Category with ID ${id} not found in branch ${branchId}`,
      );
    }

    await this.categoryRepository.remove(category);
    return { message: 'Category deleted successfully' };
  }
}
