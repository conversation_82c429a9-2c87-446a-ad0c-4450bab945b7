import { IsString, IsBoolean, IsOptional } from 'class-validator';
import { IsTimeFormat } from '../../../common/decorators/is-time-format.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCategoryDto {
  @ApiProperty({
    example: 'Category 1',
    description: 'The name of the category',
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 'Category 1 description',
    description: 'The description of the category',
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    example: 'category.png',
    description: 'The image url of the category',
  })
  @IsString()
  imageUrl: string;

  @ApiProperty({
    example: true,
    description: 'The availability status of the category',
  })
  @IsBoolean()
  @IsOptional()
  availabilityStatus: boolean;

  @ApiProperty({
    example: '10:00',
    description: 'The available from time of the category',
  })
  @IsOptional()
  @IsTimeFormat({ message: 'availableFrom must be in the format HH:mm' })
  availableFrom: string;

  @ApiProperty({
    example: '18:00',
    description: 'The available to time of the category',
  })
  @IsOptional()
  @IsTimeFormat({ message: 'availableTo must be in the format HH:mm' })
  availableTo: string;
}
