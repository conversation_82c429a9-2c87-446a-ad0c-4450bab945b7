import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  ManyToOne,
} from 'typeorm';
import { Product } from '../../product/entities/product.entity';
import { Branch } from 'src/modules/branch/entities/branch.entity';

@Entity('category')
export class Category {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: true })
  availabilityStatus: boolean;

  @Column({ nullable: true })
  availableFrom: string;

  @Column({ nullable: true })
  availableTo: string;

  @Column({ nullable: true, default: 'category.png' })
  imageUrl: string;

  @OneToMany(() => Product, (product) => product.category)
  products: Product[];

  @ManyToOne(() => Branch, (branch) => branch.categories)
  branch: Branch;
}
