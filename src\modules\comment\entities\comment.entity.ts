import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { User } from '../../user/entities/user.entity';

@Entity('comment')
export class Comment {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ nullable: true })
  entityType: string; // e.g., 'product', 'order', 'user', etc.

  @Column({ nullable: true })
  entityId: string; // ID of the entity being commented on

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isEdited: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, { nullable: true })
  author: User;

  @ManyToOne(() => Comment, (comment) => comment.replies, { nullable: true })
  parentComment: Comment;

  @OneToMany(() => Comment, (comment) => comment.parentComment)
  replies: Comment[];
}
