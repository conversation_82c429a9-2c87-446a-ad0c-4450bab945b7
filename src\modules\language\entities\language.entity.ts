import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, OneToMany } from 'typeorm';
import { Translation } from '../../translation/entities/translation.entity';

@Entity('language')
export class Language {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @OneToMany(() => Translation, (translation) => translation.language)
  translations: Translation[];
}
