import { IsEmail, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
export class CreateStoreDto {
  @ApiProperty({ example: 'Main Store', description: 'The name of the store' })
  @IsString()
  name: string;

  @ApiProperty({
    example: '123 Main St',
    description: 'The address of the store',
  })
  @IsString()
  address: string;

  @ApiProperty({
    example: '1234567890',
    description: 'The phone number of the store',
  })
  @IsString()
  phone: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the store',
  })
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'https://mainstore.com/logo.png',
    description: 'The logo URL of the store',
  })
  @IsString()
  @IsOptional()
  logoUrl: string;

  @ApiProperty({
    example: 'https://mainstore.com',
    description: 'The website of the store',
  })
  @IsString()
  @IsOptional()
  website: string;

  @ApiProperty({
    example: 'Welcome to our store!',
    description: 'The description of the store',
  })
  @IsString()
  @IsOptional()
  description: string;
}
