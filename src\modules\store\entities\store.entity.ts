import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, OneToMany } from 'typeorm';
import { Branch } from '../../branch/entities/branch.entity';
import { Loyalty } from 'src/modules/loyalty/entities/loyalty.entity';
import { Discount } from 'src/modules/discount/entities/discount.entity';

@Entity('store')
export class Store {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  name: string;

  @Column()
  address: string;

  @Column()
  phone: string;

  @Column()
  email: string;

  @Column({ nullable: true, default: 'store.png' })
  logoUrl: string;

  @Column({ nullable: true })
  website: string;

  @OneToMany(() => Branch, (branch) => branch.store)
  branches: Branch[];

  @OneToMany(() => Loyalty, (loyalty) => loyalty.store)
  loyaltyPrograms: Loyalty[];

  @OneToMany(() => Discount, (discount) => discount.store)
  discounts: Discount[];
}
