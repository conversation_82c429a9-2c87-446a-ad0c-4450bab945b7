import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Store } from './entities/store.entity';

@Injectable()
export class StoreService {
  constructor(
    @InjectRepository(Store)
    private readonly storeRepository: Repository<Store>,
  ) {}

  async create(createStoreDto: CreateStoreDto) {
    return await this.storeRepository.save(createStoreDto);
  }

  async findAll() {
    return await this.storeRepository.find();
  }

  async findOne(id: string) {
    const store = await this.storeRepository.findOne({ where: { id } });
    if (!store) {
      throw new NotFoundException('Store not found');
    }
    return store;
  }

  async update(id: string, updateStoreDto: UpdateStoreDto) {
    return await this.storeRepository.update(id, updateStoreDto);
  }

  async remove(id: string) {
    return await this.storeRepository.delete(id);
  }

  async getStoreBranches(id: string) {
    return await this.storeRepository.findOne({
      where: { id },
      relations: ['branches'],
    });
  }
}
