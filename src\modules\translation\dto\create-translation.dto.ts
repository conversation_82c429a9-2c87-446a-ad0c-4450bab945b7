import { IsOptional } from 'class-validator';
import { IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTranslationDto {
  @ApiProperty({ description: 'The translated name' })
  @IsNotEmpty()
  translatedName?: string;

  @ApiProperty({ description: 'The id of the language' })
  @IsNotEmpty()
  languageId: string;

  @ApiProperty({ description: 'The id of the product' })
  @IsOptional()
  productId?: string;

  @ApiProperty({ description: 'The id of the option' })
  @IsOptional()
  optionId?: string;

  @ApiProperty({ description: 'The id of the variation' })
  @IsOptional()
  variationId?: string;

  @ApiProperty({ description: 'The id of the category' })
  @IsOptional()
  categoryId?: string;
}
