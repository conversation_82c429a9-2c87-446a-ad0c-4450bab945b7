import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { TranslationService } from './translation.service';
import { CreateTranslationDto } from './dto/create-translation.dto';
import { UpdateTranslationDto } from './dto/update-translation.dto';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

@ApiTags('Translations')
@Controller('translations')
export class TranslationController {
  constructor(private readonly translationService: TranslationService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new translation' })
  async create(@Body() createTranslationDto: CreateTranslationDto) {
    return await this.translationService.addTranslation(createTranslationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all translations' })
  async findAll() {
    return await this.translationService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a translation by id' })
  async findOne(@Param('id') id: string) {
    return await this.translationService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a translation by id' })
  async update(
    @Param('id') id: string,
    @Body() updateTranslationDto: UpdateTranslationDto,
  ) {
    return await this.translationService.updateTranslation(
      id,
      updateTranslationDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a translation by id' })
  async remove(@Param('id') id: string) {
    return await this.translationService.remove(id);
  }

  @Get('product/:id')
  @ApiOperation({ summary: 'Get product translations by id' })
  async findProductTranslations(@Param('id') id: string) {
    return await this.translationService.findProductTranslations(id);
  }
  @Get('category/:id')
  @ApiOperation({ summary: 'Get category translations by id' })
  async findCategoryTranslations(@Param('id') id: string) {
    return await this.translationService.findCategoryTranslations(id);
  }
  @Get('option/:id')
  @ApiOperation({ summary: 'Get option translations by id' })
  async findOptionTranslations(@Param('id') id: string) {
    return await this.translationService.findOptionTranslations(id);
  }
  @Get('variation/:id')
  @ApiOperation({ summary: 'Get variation translations by id' })
  async findVariationTranslations(@Param('id') id: string) {
    return await this.translationService.findVariationTranslations(id);
  }

  @Get('product/:id/language/:languageId')
  @ApiOperation({ summary: 'Get product translations by language' })
  async findProductTranslationsByLanguage(
    @Param('id') id: string,
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findProductTranslationsByLanguage(
      id,
      languageId,
    );
  }

  @Get('category/:id/language/:languageId')
  @ApiOperation({ summary: 'Get category translations by language' })
  async findCategoryTranslationsByLanguage(
    @Param('id') id: string,
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findCategoryTranslationsByLanguage(
      id,
      languageId,
    );
  }

  @Get('option/:id/language/:languageId')
  @ApiOperation({ summary: 'Get option translations by language' })
  async findOptionTranslationsByLanguage(
    @Param('id') id: string,
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findOptionTranslationsByLanguage(
      id,
      languageId,
    );
  }

  @Get('variation/:id/language/:languageId')
  @ApiOperation({ summary: 'Get variation translations by language' })
  async findVariationTranslationsByLanguage(
    @Param('id') id: string,
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findVariationTranslationsByLanguage(
      id,
      languageId,
    );
  }

  @Get('variations/language/:languageId')
  @ApiOperation({ summary: 'Get variations translations by language' })
  async findVariationsTranslationsByLanguage(
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findVariationsTranslationsByLanguage(
      languageId,
    );
  }

  @Get('options/language/:languageId')
  @ApiOperation({ summary: 'Get options translations by language' })
  async findOptionsTranslationsByLanguage(
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findOptionsTranslationsByLanguage(
      languageId,
    );
  }

  @Get('categories/language/:languageId')
  @ApiOperation({ summary: 'Get categories translations by language' })
  async findCategoriesTranslationsByLanguage(
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findCategoriesTranslationsByLanguage(
      languageId,
    );
  }

  @Get('products/language/:languageId')
  @ApiOperation({ summary: 'Get products translations by language' })
  async findProductsTranslationsByLanguage(
    @Param('languageId') languageId: string,
  ) {
    return await this.translationService.findProductsTranslationsByLanguage(
      languageId,
    );
  }
}
