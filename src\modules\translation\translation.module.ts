import { Module } from '@nestjs/common';
import { TranslationService } from './translation.service';
import { TranslationController } from './translation.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Translation } from './entities/translation.entity';
import { Product } from '../product/entities/product.entity';
import { Category } from '../category/entities/category.entity';
import { Language } from '../language/entities/language.entity';
import { Option } from '../option/entities/option.entity';
import { Variation } from '../variation/entities/variation.entity';

@Module({
  controllers: [TranslationController],
  providers: [TranslationService],
  imports: [
    TypeOrmModule.forFeature([
      Translation,
      Product,
      Option,
      Category,
      Language,
      Variation,
    ]),
  ],
  exports: [TranslationService],
})
export class TranslationModule {}
