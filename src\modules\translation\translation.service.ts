import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateTranslationDto } from './dto/create-translation.dto';
import { UpdateTranslationDto } from './dto/update-translation.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Repository } from 'typeorm';
import { Translation } from './entities/translation.entity';
import { Product } from '../product/entities/product.entity';
import { Language } from '../language/entities/language.entity';
import { Category } from '../category/entities/category.entity';
import { Option } from '../option/entities/option.entity';
import { Variation } from '../variation/entities/variation.entity';

@Injectable()
export class TranslationService {
  constructor(
    @InjectRepository(Translation)
    private translationRepository: Repository<Translation>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Option)
    private optionRepository: Repository<Option>,
    @InjectRepository(Variation)
    private variationRepository: Repository<Variation>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Language)
    private languageRepository: Repository<Language>,
  ) {}

  async create(
    createTranslationDto: CreateTranslationDto,
  ): Promise<Translation> {
    const translation = this.translationRepository.create(createTranslationDto);
    return this.translationRepository.save(translation);
  }

  async findAll(): Promise<Translation[]> {
    return this.translationRepository.find();
  }

  async findOne(id: string): Promise<Translation> {
    return this.translationRepository.findOne({ where: { id } });
  }

  async update(
    id: string,
    updateTranslationDto: UpdateTranslationDto,
  ): Promise<Translation> {
    await this.translationRepository.update(id, updateTranslationDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const translation = await this.translationRepository.findOne({
      where: { id },
    });
    if (!translation) {
      throw new NotFoundException('Translation not found');
    }
    await this.translationRepository.delete(id);
  }

  // TODO: Add validation for the createTranslationDto or seprate dtos for each translation creation type
  async addTranslation(
    createTranslationDto: CreateTranslationDto,
  ): Promise<Translation> {
    const {
      translatedName,
      languageId,
      productId,
      optionId,
      variationId,
      categoryId,
    } = createTranslationDto;

    const language = await this.languageRepository.findOne({
      where: { id: languageId },
    });

    if (!language) {
      throw new NotFoundException('Language not found');
    }

    const translation = this.translationRepository.create({
      translatedName,
      language,
    });

    if (productId) {
      const product = await this.productRepository.findOne({
        where: { id: productId },
      });
      if (!product) {
        throw new NotFoundException('Product not found');
      }
      translation.product = product;
    } else if (optionId) {
      const option = await this.optionRepository.findOne({
        where: { id: optionId },
      });
      if (!option) {
        throw new NotFoundException('Option not found');
      }
      translation.option = option;
    } else if (variationId) {
      const variation = await this.variationRepository.findOne({
        where: { id: variationId },
      });
      if (!variation) {
        throw new NotFoundException('Variation not found');
      }
      translation.variation = variation;
    } else if (categoryId) {
      const category = await this.categoryRepository.findOne({
        where: { id: categoryId },
      });
      if (!category) {
        throw new NotFoundException('Category not found');
      }

      translation.category = category;
    } else {
      throw new NotFoundException('Invalid entity type');
    }

    return this.translationRepository.save(translation);
  }

  async updateTranslation(
    id: string,
    updateTranslationDto: UpdateTranslationDto,
  ): Promise<Translation> {
    const { translatedName } = updateTranslationDto;

    const translation = await this.translationRepository.findOne({
      where: { id },
    });
    if (!translation) {
      throw new NotFoundException('Translation not found');
    }
    translation.translatedName = translatedName;

    return this.translationRepository.save(translation);
  }

  async findProductTranslations(id: string): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { product: { id } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(`Translations not found for product ${id}`);
    }
    return translations;
  }

  async findCategoryTranslations(id: string): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { category: { id } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(`Translations not found for category ${id}`);
    }
    return translations;
  }

  async findOptionTranslations(id: string): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { option: { id } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(`Translations not found for option ${id}`);
    }
    return translations;
  }

  async findVariationTranslations(id: string): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { variation: { id } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(`Translations not found for variation ${id}`);
    }
    return translations;
  }

  async findProductTranslationsByLanguage(
    id: string,
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { product: { id }, language: { id: languageId } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(
        `Translations not found for product ${id} and language ${languageId}`,
      );
    }
    return translations;
  }

  async findCategoryTranslationsByLanguage(
    id: string,
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { category: { id }, language: { id: languageId } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(
        `Translations not found for category ${id} and language ${languageId}`,
      );
    }
    return translations;
  }

  async findOptionTranslationsByLanguage(
    id: string,
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { option: { id }, language: { id: languageId } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(
        `Translations not found for option ${id} and language ${languageId}`,
      );
    }
    return translations;
  }

  async findVariationTranslationsByLanguage(
    id: string,
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository.find({
      where: { variation: { id }, language: { id: languageId } },
    });
    if (translations.length === 0) {
      throw new NotFoundException(
        `Translations not found for variation ${id} and language ${languageId}`,
      );
    }
    return translations;
  }

  async findVariationsTranslationsByLanguage(
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository
      .createQueryBuilder('translation')
      .innerJoinAndSelect('translation.variation', 'variation')
      .innerJoinAndSelect('translation.language', 'language')
      .where('translation.language_id = :languageId', { languageId })
      .getMany();

    if (translations.length === 0) {
      throw new NotFoundException(
        `No variation translations found for language ${languageId}`,
      );
    }

    return translations;
  }

  async findOptionsTranslationsByLanguage(
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository
      .createQueryBuilder('translation')
      .innerJoinAndSelect('translation.option', 'option')
      .innerJoinAndSelect('translation.language', 'language')
      .where('translation.language_id = :languageId', { languageId })
      .getMany();

    if (translations.length === 0) {
      throw new NotFoundException(
        `No option translations found for language ${languageId}`,
      );
    }

    return translations;
  }

  async findCategoriesTranslationsByLanguage(
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository
      .createQueryBuilder('translation')
      .innerJoinAndSelect('translation.category', 'category')
      .innerJoinAndSelect('translation.language', 'language')
      .where('translation.language_id = :languageId', { languageId })
      .getMany();

    if (translations.length === 0) {
      throw new NotFoundException(
        `No category translations found for language ${languageId}`,
      );
    }

    return translations;
  }

  async findProductsTranslationsByLanguage(
    languageId: string,
  ): Promise<Translation[]> {
    const translations = await this.translationRepository
      .createQueryBuilder('translation')
      .innerJoinAndSelect('translation.product', 'product')
      .innerJoinAndSelect('translation.language', 'language')
      .where('translation.language_id = :languageId', { languageId })
      .getMany();

    if (translations.length === 0) {
      throw new NotFoundException(
        `No product translations found for language ${languageId}`,
      );
    }

    return translations;
  }

  async deleteTranslations(translations: Translation[]): Promise<void> {
    await this.translationRepository.remove(translations);
  }
}
