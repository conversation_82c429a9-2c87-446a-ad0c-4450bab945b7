import { <PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Authority } from '../../authority/entities/authority.entity';

@Entity('user_authority')
export class UserAuthority {
  @PrimaryGeneratedColumn()
  id: string;

  @ManyToOne(() => User, (user) => user.authorities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Authority, { eager: true })
  @JoinColumn({ name: 'authority_name' })
  authority: Authority;
}
