import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { UserAuthorityService } from './user-authority.service';
import { CreateUserAuthorityDto } from './dto/create-user-authority.dto';
import { UpdateUserAuthorityDto } from './dto/update-user-authority.dto';

@Controller('user-authority')
export class UserAuthorityController {
  constructor(private readonly userAuthorityService: UserAuthorityService) {}

  @Post()
  create(@Body() createUserAuthorityDto: CreateUserAuthorityDto) {
    return this.userAuthorityService.create(createUserAuthorityDto);
  }

  @Get()
  findAll() {
    return this.userAuthorityService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.userAuthorityService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateUserAuthorityDto: UpdateUserAuthorityDto,
  ) {
    return this.userAuthorityService.update(id, updateUserAuthorityDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.userAuthorityService.remove(id);
  }
}
