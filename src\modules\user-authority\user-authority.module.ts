import { Module } from '@nestjs/common';
import { UserAuthorityService } from './user-authority.service';
import { UserAuthorityController } from './user-authority.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserAuthority } from './entities/user-authority.entity';

@Module({
  controllers: [UserAuthorityController],
  providers: [UserAuthorityService],
  imports: [TypeOrmModule.forFeature([UserAuthority])],
  exports: [UserAuthorityService],
})
export class UserAuthorityModule {}
