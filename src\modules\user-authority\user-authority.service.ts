import { Injectable } from '@nestjs/common';
import { CreateUserAuthorityDto } from './dto/create-user-authority.dto';
import { UpdateUserAuthorityDto } from './dto/update-user-authority.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserAuthority } from './entities/user-authority.entity';

@Injectable()
export class UserAuthorityService {
  constructor(
    @InjectRepository(UserAuthority)
    private readonly userAuthorityRepository: Repository<UserAuthority>,
  ) {}

  create(createUserAuthorityDto: CreateUserAuthorityDto) {
    return this.userAuthorityRepository.save(createUserAuthorityDto);
  }

  findAll() {
    return this.userAuthorityRepository.find();
  }

  findOne(id: string) {
    return this.userAuthorityRepository.findOne({ where: { id } });
  }

  update(id: string, updateUserAuthorityDto: UpdateUserAuthorityDto) {
    return this.userAuthorityRepository.update(id, updateUserAuthorityDto);
  }

  remove(id: string) {
    return this.userAuthorityRepository.delete(id);
  }
}
