import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsEmail, MaxLength, MinLength, IsArray, ArrayNotEmpty, ArrayUnique, IsOptional, IsBoolean } from 'class-validator';
import { UserAuthority } from 'src/modules/user-authority/entities/user-authority.entity';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsString()
  @IsNotEmpty()
  login: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsEmail()
  email: string;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsString({ each: true })
  authorities: UserAuthority[];

  @IsString()
  @MaxLength(4)
  @MinLength(4)
  @IsOptional()
  posCode?: string = '0000';

  @IsString()
  @IsOptional()
  imageUrl?: string = '';

  @IsBoolean()
  @IsOptional()
  activated?: boolean = true;
}
