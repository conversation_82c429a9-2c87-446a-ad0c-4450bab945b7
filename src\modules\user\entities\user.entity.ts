import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserAuthority } from '../../user-authority/entities/user-authority.entity';

@Entity('user')
export class User {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ unique: true })
  email: string;

  @Column({ unique: true })
  phone: string;

  @Column()
  city: string;

  @Column()
  address: string;

  @Column({ nullable: true })
  imageUrl: string;

  @Column({ select: false, length: 60 })
  password: string;

  @Column({ default: true })
  activated: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => UserAuthority, (userAuthority) => userAuthority.user)
  authorities: UserAuthority[];

  // @OneToMany(() => UserBranchAssignment, (assignment) => assignment.user)
  // branchAssignments: UserBranchAssignment[];

  // @OneToMany(() => PosSession, (posSession) => posSession.pos, {
  //   cascade: true,
  // })
  //posSessions: PosSession[];
}
