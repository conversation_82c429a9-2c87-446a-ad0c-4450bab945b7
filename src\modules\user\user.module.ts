import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { UserRoleService } from './services/user-role.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { SharedModule } from '../shared/shared.module';
@Module({
  controllers: [UserController],
  providers: [UserService, UserRoleService],
  imports: [TypeOrmModule.forFeature([User]), SharedModule],
  exports: [UserService, UserRoleService],
})
export class UserModule {}
