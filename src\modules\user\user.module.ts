import { forward<PERSON><PERSON>, Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UserAuthority } from '../user-authority/entities/user-authority.entity';
import { SharedModule } from '../shared/shared.module';
import { UserBranchAssignment } from '../user-branch-assignment/entities/user-branch-assignment.entity';
import { Branch } from '../branch/entities/branch.entity';
import { UserBranchAssignmentModule } from '../user-branch-assignment/user-branch-assignment.module';
import { AuthorityModule } from '../authority/authority.module';
import { StoreModule } from '../store/store.module';
@Module({
  controllers: [UserController],
  providers: [UserService],
  imports: [
    TypeOrmModule.forFeature([
      User,
      Branch,
      UserAuthority,
      UserBranchAssignment,
    ]),
    SharedModule,
    AuthorityModule,
    UserBranchAssignmentModule,
    StoreModule,
  ],
  exports: [UserService],
})
export class UserModule {}
