import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Authority } from '../authority/entities/authority.entity';
import { UserAuthority } from '../user-authority/entities/user-authority.entity';
import { UserBranchAssignmentService } from '../user-branch-assignment/user-branch-assignment.service';
import { UploadService } from '../shared/upload.service';
import { AuthorityService } from '../authority/authority.service';
import { UpdateStaffDto } from './dto/update-staff-dto';
import * as bcrypt from 'bcryptjs';
import { StoreService } from '../store/store.service';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserAuthority)
    private readonly userAuthorityRepository: Repository<UserAuthority>,
    private readonly userBranchAssignmentService: UserBranchAssignmentService,
    private readonly authorityService: AuthorityService,
    private readonly uploadService: UploadService,
    private readonly storeService: StoreService,
  ) {}

  async getUserWorkspace(userId: string) {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['authorities.authority'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const roles = user.authorities.map((auth) => auth.authority.name);
    const isAdmin = roles.includes('ROLE_ADMIN');
    const isOwner = roles.includes('ROLE_OWNER');
    const isManager = roles.includes('ROLE_MANAGER');
    const isStaff = roles.includes('ROEL_STAFF');

    let stores = [];
    let branches = [];

    if (isAdmin) {
      // Admins can see all stores
      stores = await this.storeService.findAll();
    } else if (isOwner) {
      // Owners can see their stores
      stores = await this.getUserStores(userId);
    }

    if (isAdmin || isOwner) {
      // For admins/owners, get branches when they select a store (frontend will request)
    } else if (isManager || isStaff) {
      // For managers/staff, get their assigned branches directly
      branches = await this.userBranchAssignmentService.getUserBranches(userId);
    }

    return {
      roles,
      isAdmin,
      isOwner,
      isManager,
      isStaff,
      stores,
      branches,
    };
  }

  async getUserWithRelations(userId: string) {
    return this.userRepository.findOne({
      where: { id: userId },
      relations: [
        'authorities',
        'authorities.authority',
        'branchAssignments',
        'branchAssignments.branch',
      ],
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        login: true,
        imageUrl: true,
        activated: true,
        posCode: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  async create(createUserData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    login: string;
  }) {
    return await this.userRepository.save(createUserData);
  }

  async findAll() {
    return await this.userRepository.find();
  }

  async findOne(id: string) {
    return await this.userRepository.findOne({
      where: { id },
      relations: ['authorities', 'authorities.authority'],
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const { firstName, lastName, imageUrl } = updateUserDto;
    if (firstName) {
      user.firstName = firstName;
    }
    if (lastName) {
      user.lastName = lastName;
    }
    if (imageUrl) {
      if (user.imageUrl) {
        await this.uploadService.deleteFile(user.imageUrl);
      }
      user.imageUrl = imageUrl;
    }
    return this.userRepository.save(user);
  }

  async remove(id: string) {
    return await this.userRepository.delete(id);
  }

  async findByEmail(email: string): Promise<User | undefined> {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.authorities', 'userAuthorities')
      .leftJoinAndSelect('userAuthorities.authority', 'authority')
      .addSelect('user.password')
      .where('user.email = :email', { email })
      .getOne();
  }

  async assignRole(userId: string, authority: Authority): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const userAuthority = this.userAuthorityRepository.create({
      user,
      authority,
    });
    await this.userAuthorityRepository.save(userAuthority);
  }

  async getUserBranches(userId: string) {
    const userBranches =
      await this.userBranchAssignmentService.getUserBranches(userId);
    if (userBranches.length === 0) {
      throw new NotFoundException(
        'User has no branches assigned or user not found',
      );
    }
    return userBranches.map((userBranch) => userBranch.branch);
  }

  async getUserStores(userId: string) {
    const userBranches = await this.getUserBranches(userId);
    return userBranches.map((userBranch) => userBranch.store);
  }

  async findByPosCode(code: string) {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.authorities', 'userAuthorities')
      .leftJoinAndSelect('userAuthorities.authority', 'authority')
      .where('user.posCode = :code', { code })
      .getOne();
  }

  async generateUniquePosCode(): Promise<string> {
    let posCode: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 100;

    while (!isUnique && attempts < maxAttempts) {
      posCode = this.generateRandomPosCode();
      const existingUser = await this.userRepository.findOne({
        where: { posCode },
      });

      if (!existingUser) {
        isUnique = true;
      }
      attempts++;
    }

    if (!isUnique) {
      throw new Error(
        'Failed to generate unique POS code after multiple attempts',
      );
    }

    return posCode;
  }

  private generateRandomPosCode(): string {
    // Generate a 4-digit number, pad with leading zeros if necessary
    const randomNum = Math.floor(Math.random() * 10000);
    return randomNum.toString().padStart(4, '0');
  }

  async validatePosCode(posCode: string): Promise<boolean> {
    const user = await this.userRepository.findOne({ where: { posCode } });
    return !user; // Returns true if code is available
  }

  // ADMIN METHODS
  async findAllAdmin() {
    return await this.userRepository.find({
      relations: ['authorities', 'branchAssignments'],
    });
  }

  async findOneAdmin(id: string) {
    return await this.userRepository.findOne({
      where: { id },
      relations: [
        'authorities',
        'branchAssignments',
        'branchAssignments.branch',
      ],
    });
  }

  async createUserByAdmin(createUserDto: CreateUserDto, branchId: string) {
    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    // Create user with STAFF role
    const user = await this.userRepository.save({
      ...createUserDto,
      password: hashedPassword,
    });
    const authority = await this.authorityService.findOne(
      (createUserDto.authorities[0] as any) || 'ROLE_STAFF',
    );

    // Assign role to user
    await this.assignRole(user.id, authority);

    // Assign user to branch
    if (branchId) {
      await this.userBranchAssignmentService.assignUserToBranch(
        user.id,
        branchId,
      );
    }

    return user;
  }

  async updateAdmin(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return await this.userRepository.save({ ...user, ...updateUserDto });
  }

  async removeAdmin(id: string) {
    // Add any admin-specific cleanup here
    return await this.userRepository.delete(id);
  }

  async toggleUserStatus(id: string, active: boolean) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    user.activated = active;
    return await this.userRepository.save(user);
  }

  async findUsersByStore(storeId: string) {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.branchAssignments', 'branchAssignments')
      .leftJoinAndSelect('branchAssignments.branch', 'branch')
      .where('branch.store.id = :storeId', { storeId })
      .getMany();
  }

  async findUsersByBranch(branchId: string) {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .leftJoin('branch.store', 'store')
      .leftJoinAndSelect('user.authorities', 'userAuthority')
      .leftJoinAndSelect('userAuthority.authority', 'authority')
      .where('branch.id = :branchId', { branchId })
      .getMany();
  }

  // OWNER METHODS
  async findUsersUnderOwner(ownerId: string) {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .leftJoin('branch.store', 'store')
      .where('store.owner.id = :ownerId', { ownerId })
      .getMany();
  }

  async findUserInOwnerScope(userId: string, branchId: string) {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .where('user.id = :userId', { userId })
      .andWhere('branch.id = :branchId', { branchId })
      .getOne();

    if (!user) {
      throw new NotFoundException('User not found in your scope');
    }
    return user;
  }

  async createUserByOwner(createUserDto: CreateUserDto, branchId: string) {
    const allowedRoles = ['STAFF', 'MANAGER'];

    if (
      !createUserDto.authorities.every((role) =>
        allowedRoles.includes(role.authority.name),
      )
    ) {
      throw new BadRequestException('Can only create MANAGER or STAFF users');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    // Create user with STAFF role
    const user = await this.userRepository.save({
      ...createUserDto,
      password: hashedPassword,
    });
    const authority = await this.authorityService.findOne('ROLE_STAFF');

    // Assign role to user
    await this.assignRole(user.id, authority);

    // Assign user to branch
    if (branchId) {
      await this.userBranchAssignmentService.assignUserToBranch(
        user.id,
        branchId,
      );
    }

    return user;
  }

  async updateUserByOwner(
    id: string,
    updateUserDto: UpdateUserDto,
    ownerId: string,
    branchId: string,
  ) {
    // First verify user is in owner's scope
    //await this.findUserInOwnerScope(id, ownerId);
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return await this.userRepository.save({ ...user, ...updateUserDto });
  }

  async deleteUserByOwner(id: string, ownerId: string) {
    // First verify user is in owner's scope
    await this.findUserInOwnerScope(id, ownerId);
    return this.remove(id);
  }

  async toggleUserStatusByOwner(
    id: string,
    active: boolean,
    ownerId: string,
    branchId: string,
  ) {
    const user = await this.findUserInOwnerScope(id, branchId);
    user.activated = active;
    return this.userRepository.save(user);
  }

  async updateUserRoleByOwner(id: string, role: string, ownerId: string) {
    if (!['MANAGER', 'STAFF'].includes(role)) {
      throw new BadRequestException('Can only assign MANAGER or STAFF roles');
    }

    const user = await this.findUserInOwnerScope(id, ownerId);
    return this.updateUserRole(user.id, role);
  }

  async findUsersByStoreForOwner(storeId: string, ownerId: string) {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .leftJoin('branch.store', 'store')
      .where('store.id = :storeId', { storeId })
      .andWhere('store.ownerId = :ownerId', { ownerId })
      .getMany();
  }

  async findUsersByBranchForOwner(branchId: string, ownerId: string) {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .leftJoin('branch.store', 'store')
      .leftJoinAndSelect('user.authorities', 'userAuthority')
      .leftJoinAndSelect('userAuthority.authority', 'authority')
      .where('branch.id = :branchId', { branchId })
      .andWhere('authority.name IN (:...allowedRoles)', {
        allowedRoles: ['ROLE_OWNER', 'ROLE_MANAGER', 'ROLE_STAFF'],
      })
      .getMany();
  }

  // MANAGER METHODS
  async findStaffInManagerBranches(managerId: string, branchId: string) {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .leftJoinAndSelect('user.authorities', 'userAuthority')
      .leftJoinAndSelect('userAuthority.authority', 'authority')
      .where('branch.id = :branchId', { branchId })
      .andWhere('authority.name IN (:...allowedRoles)', {
        allowedRoles: ['ROLE_OWNER', 'ROLE_MANAGER', 'ROLE_STAFF'],
      })
      .getMany();
  }

  async findStaffInManagerScope(
    staffId: string,
    managerId: string,
    branchId: string,
  ) {
    const staff = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoin('branchAssignments.branch', 'branch')
      .leftJoinAndSelect('user.authorities', 'userAuthority')
      .leftJoinAndSelect('userAuthority.authority', 'authority')
      .where('user.id = :staffId', { staffId })
      .andWhere('branch.id = :branchId', { branchId })
      .andWhere('authority.name IN (:...allowedRoles)', {
        allowedRoles: ['ROLE_OWNER', 'ROLE_MANAGER', 'ROLE_STAFF'],
      })
      .getOne();

    if (!staff) {
      throw new NotFoundException('Staff member not found in your branches');
    }
    return staff;
  }

  async createStaff(
    createStaffDto: CreateUserDto,
    managerId: string,
    branchId: string,
  ) {
    // Verify manager has access to the branch if branchId is provided
    if (branchId) {
      const canAssign = await this.canManagerAssignToBranch(
        managerId,
        branchId,
      );
      if (!canAssign) {
        throw new ForbiddenException('You cannot assign staff to this branch');
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createStaffDto.password, 10);
    // Create user with STAFF role
    const user = await this.userRepository.save({
      ...createStaffDto,
      password: hashedPassword,
    });
    const authority = await this.authorityService.findOne('ROLE_STAFF');

    // Assign role to user
    await this.assignRole(user.id, authority);

    // Assign user to branch
    if (branchId) {
      await this.userBranchAssignmentService.assignUserToBranch(
        user.id,
        branchId,
      );
    }

    return user;
  }

  async updateStaff(
    id: string,
    updateStaffDto: UpdateStaffDto,
    managerId: string,
    branchId: string,
  ) {
    // First verify staff is in manager's scope
    await this.findStaffInManagerScope(id, managerId, branchId);

    const userToUpdate = await this.userRepository.findOne({
      where: { id },
      relations: ['authorities'],
    });

    if (!userToUpdate) {
      throw new NotFoundException('User not found');
    }

    // Prevent role elevation
    if (
      userToUpdate.authorities &&
      userToUpdate.authorities.some((role) => role.authority.name !== 'STAFF')
    ) {
      throw new ForbiddenException('You can only update STAFF members');
    }

    const { firstName, lastName, email, posCode, imageUrl } = updateStaffDto;
    if (firstName) {
      userToUpdate.firstName = firstName;
    }
    if (lastName) {
      userToUpdate.lastName = lastName;
    }
    if (email) {
      userToUpdate.email = email;
    }
    if (posCode) {
      userToUpdate.posCode = posCode;
    }
    if (imageUrl) {
      if (userToUpdate.imageUrl) {
        await this.uploadService.deleteFile(userToUpdate.imageUrl);
      }
      userToUpdate.imageUrl = imageUrl;
    }
    return this.userRepository.save(userToUpdate);
  }

  async deleteStaff(id: string, managerId: string, branchId: string) {
    // First verify staff is in manager's scope
    await this.findStaffInManagerScope(id, managerId, branchId);
    return this.remove(id);
  }

  async toggleStaffStatus(
    id: string,
    active: boolean,
    managerId: string,
    branchId: string,
  ) {
    const staff = await this.findStaffInManagerScope(id, managerId, branchId);
    staff.activated = active;
    return this.userRepository.save(staff);
  }

  async findUsersInManagerBranch(branchId: string, managerId: string) {
    // First verify manager has access to this branch
    const canAccess = await this.canManagerAccessBranch(managerId, branchId);
    if (!canAccess) {
      throw new ForbiddenException('You do not have access to this branch');
    }

    return this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.branchAssignments', 'branchAssignments')
      .leftJoinAndSelect('user.authorities', 'userAuthority')
      .leftJoinAndSelect('userAuthority.authority', 'authority')
      .where('branchAssignments.branch.id = :branchId', { branchId })
      .andWhere('authority.name IN (:...allowedRoles)', {
        allowedRoles: ['ROLE_MANAGER', 'ROLE_STAFF'],
      })
      .getMany();
  }

  private async canManagerAssignToBranch(
    managerId: string,
    branchId: string,
  ): Promise<boolean> {
    return true;
  }

  private async canManagerAccessBranch(
    managerId: string,
    branchId: string,
  ): Promise<boolean> {
    return this.canManagerAssignToBranch(managerId, branchId);
  }

  private async updateUserRole(id: string, role: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['authorities'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if the role exists in the database
    const authority = await this.authorityService.findOne(role);
    if (!authority) {
      throw new NotFoundException(`Role '${role}' not found`);
    }

    // Remove all existing roles (assuming single-role assignment)
    await this.userAuthorityRepository.delete({ user: { id } });

    // Create and save the new user authority
    const userAuthority = this.userAuthorityRepository.create({
      user,
      authority,
    });

    await this.userAuthorityRepository.save(userAuthority);

    // Return updated user with authorities
    return this.userRepository.findOne({
      where: { id },
      relations: ['authorities', 'authorities.authority'],
    });
  }
}
